# app/previews/school_mailer_preview.rb
# Note: The class name must match the pattern ApplicationMailerPreview for Rails to find it
class SchoolMailerPreview < ActionMailer::Preview
  # Preview all emails at http://localhost:3000/rails/mailers/school_mailer

  def welcome_email
    SchoolMailer.welcome_email(sample_teacher, 'text-new-password')
  end

  def password_reset
    SchoolMailer.password_reset(sample_teacher, 'test-token')
  end

  def password_changed
    SchoolMailer.password_changed(sample_teacher, 'NewP@ssw0rd123!')
  end

  def school_invite_request
    SchoolMailer.school_invite_request('<PERSON>', '<EMAIL>', sample_school)
  end

  def exemplar_work_single_submitted
    SchoolMailer.exemplar_work_submitted(sample_teacher, sample_exemplar_work(:pending))
  end

  def exemplar_work_multiple_submitted
    SchoolMailer.exemplar_work_submitted(sample_teacher, sample_exemplar_work(:pending, 2))
  end

  def exemplar_work_approved
    SchoolMailer.exemplar_work_approved(sample_teacher, sample_exemplar_work(:approved))
  end

  def lesson_feedback_submitted
    SchoolMailer.lesson_feedback_submitted(sample_teacher, sample_feedback)
  end

  def lesson_feedback_received_comment
    SchoolMailer.lesson_feedback_received_comment(sample_teacher, sample_feedback, "Thank you for your feedback!")
  end

  def homework_submission_first
    SchoolMailer.homework_submission(sample_homework, "first")
  end

  def homework_submission_last
    SchoolMailer.homework_submission(sample_homework, "last")
  end

  def weekly_digest
    SchoolMailer.weekly_digest(Teacher.find_by(email: '<EMAIL>'))
  end

  def school_weekly_digest
    SchoolMailer.school_weekly_digest(Teacher.find_by(email: '<EMAIL>'))
  end

  private

  def sample_teacher
    Teacher.where.not(wonde_id: nil).joins(:school).last || 
    Teacher.joins(:school).last ||
    create_sample_teacher
  end

  def sample_school
    School.joins(:users).where(users: { is_school_admin: true }).last ||
    create_sample_school
  end

  def sample_exemplar_work(status, count = 1)
    ExemplarWork.where(status: status).last(count) ||
    create_sample_exemplar_works(status, count)
  end

  def sample_feedback
    NewPresentationFeedback.where(associate_to: "template").last
  end

  def sample_homework
    Homework
      .joins(:submissions)
      .where.not(homework_task_submissions: { complete_at: nil })
      .distinct
      .last
  end

  def create_sample_teacher
    # Fallback if no teachers exist in the database
    teacher = Teacher.new(
      name: "John Smith",
      email: "<EMAIL>",
      job_title: "Mathematics Teacher"
    )
    
    # Mock the school association
    def teacher.school
      OpenStruct.new(name: "Sample School")
    end
    
    # Ensure id exists for any URL generation
    def teacher.id
      123
    end
    
    teacher
  end

  def create_sample_school
    school = School.new(name: "Sample High School")
    
    # Mock the main_teacher association
    def school.main_teacher
      teacher = OpenStruct.new(
        name: "Admin Teacher",
        email: "<EMAIL>",
        is_school_admin: true
      )
      teacher
    end
    
    def school.id
      456
    end
    
    school
  end

  def create_sample_exemplar_works(status, count)
    exemplar_works = []
    count.times do |i|
      exemplar_works << ExemplarWork.new(
        fileboy_id: "sample-fileboy-id",
        status: status,
        title: "Sample Title (#{i+1})",
        body: "Sample Text",
        display_name: "Sample Display Name (#{i+1})"
      )
    end

    exemplar_works
  end
end
